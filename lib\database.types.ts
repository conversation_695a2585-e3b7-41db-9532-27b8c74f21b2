export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      units: {
        Row: {
          id: number
          name: string
          address: string
          city: string
          state: string
          email: string
          phone: string | null
          postal_code: string | null
          latitude: number | null
          longitude: number | null
          google_maps_url: string | null
          maps_url: string | null
          image_url: string | null
          active: boolean
          created_at: string
          updated_at: string
          operating_hours: Json | null
        }
        Insert: {
          id?: number
          name: string
          address: string
          city?: string
          state?: string
          email: string
          phone?: string | null
          postal_code?: string | null
          latitude?: number | null
          longitude?: number | null
          google_maps_url?: string | null
          maps_url?: string | null
          image_url?: string | null
          active?: boolean
          created_at?: string
          updated_at?: string
          operating_hours?: Json | null
        }
        Update: {
          id?: number
          name?: string
          address?: string
          city?: string
          state?: string
          email?: string
          phone?: string | null
          postal_code?: string | null
          latitude?: number | null
          longitude?: number | null
          google_maps_url?: string | null
          maps_url?: string | null
          image_url?: string | null
          active?: boolean
          updated_at?: string
          operating_hours?: Json | null
        }
      },
      
      users: {
        Row: {
          id: number
          name: string
          email: string
          password?: string
          role_id: number
          active: boolean
          created_at: string
          updated_at: string
          avatar_url?: string | null
          phone?: string | null
          position?: string | null
          department?: string | null
          bio?: string | null
        }
        Insert: {
          id?: number
          name: string
          email: string
          password?: string
          role_id: number
          active?: boolean
          created_at?: string
          updated_at?: string
          avatar_url?: string | null
          phone?: string | null
          position?: string | null
          department?: string | null
          bio?: string | null
        }
        Update: {
          id?: number
          name?: string
          email?: string
          password?: string
          role_id?: number
          active?: boolean
          created_at?: string
          updated_at?: string
          avatar_url?: string | null
          phone?: string | null
          position?: string | null
          department?: string | null
          bio?: string | null
        }
      }
      products: {
        Row: {
          id: number
          name: string
          slug: string
          sku: string
          description: string
          short_description: string
          price: number
          sale_price: number | null
          stock: number
          category_id: number
          brand: string
          featured: boolean
          active: boolean
          created_at: string
          updated_at: string
          video_url: string | null
        }
        Insert: {
          id?: number
          name: string
          slug: string
          sku: string
          description: string
          short_description: string
          price: number
          sale_price?: number | null
          stock: number
          category_id: number
          brand: string
          featured?: boolean
          active?: boolean
          created_at?: string
          updated_at?: string
          video_url?: string | null
        }
        Update: {
          id?: number
          name?: string
          slug?: string
          sku?: string
          description?: string
          short_description?: string
          price?: number
          sale_price?: number | null
          stock?: number
          category_id?: number
          brand?: string
          featured?: boolean
          active?: boolean
          created_at?: string
          updated_at?: string
          video_url?: string | null
        }
      }
      product_images: {
        Row: {
          id: number
          product_id: number
          url: string  // ✅ REAL: coluna é 'url' (não 'image_url')
          alt_text: string | null
          is_main: boolean  // ✅ REAL: coluna é 'is_main' (não 'is_primary')
          display_order: number
          created_at: string
        }
        Insert: {
          id?: number
          product_id: number
          url: string  // ✅ REAL: coluna é 'url' (não 'image_url')
          alt_text?: string | null
          is_main?: boolean  // ✅ REAL: coluna é 'is_main' (não 'is_primary')
          display_order?: number
          created_at?: string
        }
        Update: {
          id?: number
          product_id?: number
          url?: string  // ✅ REAL: coluna é 'url' (não 'image_url')
          alt_text?: string | null
          is_main?: boolean  // ✅ REAL: coluna é 'is_main' (não 'is_primary')
          display_order?: number
          created_at?: string
        }
      }
      categories: {
        Row: {
          id: number
          name: string
          slug: string
          description: string | null
          parent_id: number | null
          active: boolean
          created_at: string
          updated_at: string
          image_url: string | null
        }
        Insert: {
          id?: number
          name: string
          slug: string
          description?: string | null
          parent_id?: number | null
          active?: boolean
          created_at?: string
          updated_at?: string
          image_url?: string | null
        }
        Update: {
          id?: number
          name?: string
          slug?: string
          description?: string | null
          parent_id?: number | null
          active?: boolean
          created_at?: string
          updated_at?: string
          image_url?: string | null
        }
      }
      slides: {
        Row: {
          id: number
          title: string
          subtitle: string | null
          image_url: string
          link_url: string | null
          button_text: string | null
          display_order: number
          active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          title: string
          subtitle?: string | null
          image_url: string
          link_url?: string | null
          button_text?: string | null
          display_order: number
          active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          title?: string
          subtitle?: string | null
          image_url?: string
          link_url?: string | null
          button_text?: string | null
          display_order?: number
          active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: number
          user_id: number
          title: string
          message: string
          read: boolean
          created_at: string
        }
        Insert: {
          id?: number
          user_id: number
          title: string
          message: string
          read?: boolean
          created_at?: string
        }
        Update: {
          id?: number
          user_id?: number
          title?: string
          message?: string
          read?: boolean
          created_at?: string
        }
      }
      roles: {
        Row: {
          id: number
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      units: {
        Row: {
          id: number
          name: string
          address: string
          city: string
          state: string
          postal_code: string
          phone: string
          email: string | null
          active: boolean
          created_at: string
          updated_at: string
          latitude: number | null
          longitude: number | null
        }
        Insert: {
          id?: number
          name: string
          address: string
          city: string
          state: string
          postal_code: string
          phone: string
          email?: string | null
          active?: boolean
          created_at?: string
          updated_at?: string
          latitude?: number | null
          longitude?: number | null
        }
        Update: {
          id?: number
          name?: string
          address?: string
          city?: string
          state?: string
          postal_code?: string
          phone?: string
          email?: string | null
          active?: boolean
          created_at?: string
          updated_at?: string
          latitude?: number | null
          longitude?: number | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
