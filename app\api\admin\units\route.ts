import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/database.types'

export async function POST(req: NextRequest) {
  const cookieStore = cookies()
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore })

  try {
    console.log('🏢 [UNITS API] POST - Criando nova unidade')

    // TEMPORÁRIO: Remover verificação de autenticação para permitir cadastro
    // TODO: Implementar autenticação adequada depois
    console.log('⚠️ [UNITS API] Autenticação temporariamente desabilitada para testes')

    const formData = await req.json();
    console.log('📦 API /admin/units: Payload recebido para adicionar unidade:', formData);

    // Preparar dados para inserção incluindo novos campos
    const insertData = {
      name: formData.name,
      address: formData.address,
      city: formData.city || 'Cuiabá',
      state: formData.state || 'MT',
      email: formData.email,
      phone: formData.phone || formData.whatsapp || null,
      postal_code: formData.postal_code || null,
      latitude: formData.latitude || null,
      longitude: formData.longitude || null,
      maps_url: formData.maps_url || formData.googleMapsLink || null,
      active: formData.active !== false,
      operating_hours: formData.operating_hours || null,
      image_url: formData.image_url || formData.image || null
    };
    
    console.log('💾 Dados preparados para inserção:', JSON.stringify(insertData, null, 2));

    console.log('💾 Dados preparados para inserção:', JSON.stringify(insertData, null, 2));

    const { data, error } = await supabase
      .from('units')
      .insert([insertData])
      .select();

    if (error) {
      console.error('❌ ERRO CRÍTICO SUPABASE INSERT (units) - DETALHES:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        receivedPayload: formData
      });
      return NextResponse.json({
        success: false,
        error: error.message || 'Falha ao adicionar unidade: Erro desconhecido do Supabase',
        details: error.details,
        code: error.code,
        hint: error.hint
      }, { status: 500 });
    }

    console.log('✅ Unidade adicionada com sucesso:', data);
    return NextResponse.json({ success: true, data: data[0] }, { status: 201 });

    } catch (e: unknown) {
      const error = e as Error;
      console.error('🚨 Erro inesperado na API /admin/units (fora do Supabase):', error?.message || 'Erro desconhecido');
      return NextResponse.json({ 
        success: false, 
        error: error?.message || 'Erro interno do servidor',
        stack: process.env.NODE_ENV === 'development' ? error?.stack : undefined
      }, { status: 500 });
    }
}

export async function PUT(req: NextRequest) {
  const cookieStore = cookies()
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore })

  try {
    console.log('🏢 [UNITS API] PUT - Atualizando unidade')

    // TEMPORÁRIO: Remover verificação de autenticação para permitir atualização
    // TODO: Implementar autenticação adequada depois
    console.log('⚠️ [UNITS API] Autenticação temporariamente desabilitada para testes')

    // Obter dados do corpo da requisição
    const body = await req.json()
    console.log('📋 [UNITS API] Dados recebidos para atualização:', JSON.stringify(body, null, 2))

    // Validar campos obrigatórios
    const requiredFields = ['id', 'name', 'address', 'email']
    for (const field of requiredFields) {
      if (!body[field]) {
        console.log(`❌ [UNITS API] Campo obrigatório ausente: ${field}`)
        return NextResponse.json({
          error: `Campo obrigatório: ${field}`,
          field: field,
          received: body
        }, { status: 400 })
      }
    }

    // Preparar dados para atualização (usando estrutura real descoberta)
    const updateData = {
      name: body.name,
      address: body.address,
      city: body.city || 'Cuiabá',
      state: body.state || 'MT',
      email: body.email,
      postal_code: body.postal_code || body.zip_code || null,
      phone: body.phone || body.whatsapp || null,
      latitude: body.latitude || null,
      longitude: body.longitude || null,
      maps_url: body.maps_url || body.googleMapsLink || null,
      image_url: body.image_url || body.image || null,
      operating_hours: body.operating_hours || null
    }

    console.log('💾 [UNITS API] Dados preparados para atualização:', JSON.stringify(updateData, null, 2))

    // Atualizar no banco de dados
    const { data, error } = await supabase
      .from('units')
      .update(updateData)
      .eq('id', body.id)
      .select()
      .single()

    if (error) {
      console.error('❌ [UNITS API] ERRO DETALHADO DO SUPABASE (UPDATE):')
      console.error('❌ [UNITS API] Message:', error.message)
      console.error('❌ [UNITS API] Details:', error.details)
      console.error('❌ [UNITS API] Hint:', error.hint)
      console.error('❌ [UNITS API] Code:', error.code)
      console.error('❌ [UNITS API] Error Object:', JSON.stringify(error, null, 2))

      return NextResponse.json({
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        supabaseError: error
      }, { status: 500 })
    }

    console.log('✅ [UNITS API] Unidade atualizada com sucesso:', JSON.stringify(data, null, 2))
    return NextResponse.json(data)

  } catch (error: any) {
    console.error('❌ [UNITS API] Erro interno completo (PUT):', error?.message || error)
    console.error('❌ [UNITS API] Stack trace:', error?.stack)
    return NextResponse.json({
      error: 'Erro interno do servidor',
      details: error?.message || 'Erro desconhecido',
      stack: error?.stack
    }, { status: 500 })
  }
}

export async function GET(req: NextRequest) {
  const cookieStore = cookies()
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore })

  try {
    console.log('🏢 [UNITS API] GET - Buscando unidades')

    // TEMPORÁRIO: Remover verificação de autenticação para permitir busca
    // TODO: Implementar autenticação adequada depois
    console.log('⚠️ [UNITS API] Autenticação temporariamente desabilitada para testes')

    // Buscar unidades
    const { data, error } = await supabase
      .from('units')
      .select('*')
      .eq('active', true)
      .order('name')

    if (error) {
      console.error('❌ [UNITS API] Erro ao buscar unidades:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('✅ [UNITS API] Unidades encontradas:', data.length)
    return NextResponse.json(data)

  } catch (error) {
    console.error('❌ [UNITS API] Erro interno:', error)
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 })
  }
}
