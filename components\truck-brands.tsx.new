"use client"

import Image, { ImageProps } from "next/image"
import { useRef, useEffect, useState } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"

interface Brand {
  id: number
  name: string
  logo: string
}

export function TruckBrands() {
  const [isMounted, setIsMounted] = useState(false)
  
  const brands: Brand[] = [
    {
      id: 1,
      name: "Scania",
      logo: "/svg/marcas/scania.svg",
    },
    {
      id: 2,
      name: "Mercedes-Benz",
      logo: "/svg/marcas/mercedes.svg",
    },
    {
      id: 3,
      name: "<PERSON>",
      logo: "/svg/marcas/ford.svg",
    },
    {
      id: 4,
      name: "MA<PERSON>",
      logo: "/svg/marcas/man.svg",
    },
    {
      id: 5,
      name: "Randon",
      logo: "/svg/marcas/randon.svg",
    },
    {
      id: 6,
      name: "D<PERSON>",
      logo: "/svg/marcas/DAF.svg",
    },
  ]

  const carouselRef = useRef<HTMLDivElement | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [scrollLeft, setScrollLeft] = useState(0)

  // Only run on client-side
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Auto-scroll effect
  useEffect(() => {
    if (!isMounted) return
    
    const carousel = carouselRef.current
    if (!carousel) return

    let animationId: number
    let isPaused = false
    let position = 0
    const speed = 0.5 // Reduced speed for smoother animation

    const animate = () => {
      if (!isPaused && carousel && !isDragging) {
        position += speed
        const halfWidth = carousel.scrollWidth / 2
        
        if (position >= halfWidth) {
          position = 0
          carousel.scrollLeft = 0
        } else {
          carousel.scrollLeft = position
        }
      }
      animationId = requestAnimationFrame(animate)
    }

    animationId = requestAnimationFrame(animate)

    const handleMouseEnter = () => {
      isPaused = true
      carousel.style.cursor = 'grab'
    }

    const handleMouseLeave = () => {
      isPaused = false
      carousel.style.cursor = ''
    }

    const handleMouseDown = (e: MouseEvent) => {
      isPaused = true
      setIsDragging(true)
      setStartX(e.pageX - carousel.offsetLeft)
      setScrollLeft(carousel.scrollLeft)
      carousel.style.cursor = 'grabbing'
    }

    const handleMouseUp = () => {
      setIsDragging(false)
      isPaused = false
      carousel.style.cursor = 'grab'
    }

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return
      e.preventDefault()
      const x = e.pageX - carousel.offsetLeft
      const walk = (x - startX) * 2 // Scroll faster
      carousel.scrollLeft = scrollLeft - walk
    }

    // Touch events for mobile
    const handleTouchStart = (e: TouchEvent) => {
      isPaused = true
      const touch = e.touches[0]
      setStartX(touch.pageX - carousel.offsetLeft)
      setScrollLeft(carousel.scrollLeft)
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isDragging) return
      const touch = e.touches[0]
      const x = touch.pageX - carousel.offsetLeft
      const walk = (x - startX) * 2
      carousel.scrollLeft = scrollLeft - walk
    }

    carousel.addEventListener('mouseenter', handleMouseEnter)
    carousel.addEventListener('mouseleave', handleMouseLeave)
    carousel.addEventListener('mousedown', handleMouseDown as any)
    carousel.addEventListener('mouseup', handleMouseUp)
    carousel.addEventListener('mousemove', handleMouseMove as any)
    carousel.addEventListener('mouseleave', handleMouseUp)
    carousel.addEventListener('touchstart', handleTouchStart as any)
    carousel.addEventListener('touchmove', handleTouchMove as any, { passive: false })
    carousel.addEventListener('touchend', handleMouseUp)

    return () => {
      cancelAnimationFrame(animationId)
      carousel.removeEventListener('mouseenter', handleMouseEnter)
      carousel.removeEventListener('mouseleave', handleMouseLeave)
      carousel.removeEventListener('mousedown', handleMouseDown as any)
      carousel.removeEventListener('mouseup', handleMouseUp)
      carousel.removeEventListener('mousemove', handleMouseMove as any)
      carousel.removeEventListener('touchstart', handleTouchStart as any)
      carousel.removeEventListener('touchmove', handleTouchMove as any)
      carousel.removeEventListener('touchend', handleMouseUp)
    }
  }, [isMounted, isDragging, startX, scrollLeft])

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = e.target as HTMLImageElement
    target.onerror = null
    target.src = '/images/placeholder-brand.png'
  }

  const navigate = (direction: 'prev' | 'next') => {
    if (!carouselRef.current) return
    
    const carousel = carouselRef.current
    const scrollAmount = carousel.offsetWidth * 0.8 // Scroll 80% of container width
    
    carousel.scrollTo({
      left: direction === 'next' 
        ? carousel.scrollLeft + scrollAmount 
        : carousel.scrollLeft - scrollAmount,
      behavior: 'smooth'
    })
  }

  if (!isMounted) {
    return (
      <section className="py-12 relative">
        <div className="h-16 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse max-w-6xl mx-auto" />
      </section>
    )
  }

  return (
    <section className="py-12 relative">
      <h1 className="text-3xl font-bold text-center mb-8">
        As melhores marcas automotivas você encontra aqui!
      </h1>

      <div className="relative max-w-6xl mx-auto px-4">
        <div className="overflow-hidden">
          <div
            ref={carouselRef}
            className="flex transition-all duration-300 ease-out space-x-6 overflow-x-auto scrollbar-hide"
            style={{ WebkitOverflowScrolling: 'touch', scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {[...brands, ...brands].map((brand, index) => (
              <div
                key={`${brand.id}-${index}`}
                className="flex-shrink-0 w-40 flex items-center justify-center transition-transform duration-300 hover:scale-105"
              >
                <div className="relative w-32 h-16">
                  <Image
                    src={brand.logo}
                    alt={brand.name}
                    fill
                    className="object-contain"
                    onError={handleImageError}
                    unoptimized={brand.logo.endsWith('.svg')}
                    loading={index > 5 ? 'lazy' : 'eager'}
                    sizes="(max-width: 768px) 160px, 200px"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <Button
          variant="outline"
          size="icon"
          className="absolute left-0 top-1/2 -translate-y-1/2 rounded-full z-10 hidden md:flex"
          onClick={() => navigate('prev')}
          aria-label="Anterior"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="absolute right-0 top-1/2 -translate-y-1/2 rounded-full z-10 hidden md:flex"
          onClick={() => navigate('next')}
          aria-label="Próximo"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </section>
  )
}
