import Image from "next/image"
import Link from "next/link"
import { AdminLogin } from "@/components/admin-login"

export default function AdminPage() {
  return (
    <div className="min-h-screen flex flex-col bg-zantrix">
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <Link href="/">
              <Image
                src="/placeholder.svg?height=60&width=180"
                alt="Grupo Central Logo"
                width={180}
                height={60}
                className="h-12 mx-auto mb-6"
              />
            </Link>
            <h1 className="text-2xl font-bold">Zantrix Digital</h1>
            <p className=" mt-2">Faça login para acessar o sistema</p>
          </div>

          <AdminLogin />
        </div>
      </div>

      <footer className="py-4 text-center text-gray-600 text-sm">
        &copy; {new Date().getFullYear()} Grupo Central. Todos os direitos reservados.
      </footer>
    </div>
  )
}
