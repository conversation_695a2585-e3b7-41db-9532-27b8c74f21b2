-- Script para adicionar a coluna maps_url à tabela units e popular com os endereços reais

-- 1. <PERSON><PERSON>, adicione a coluna maps_url se ela não existir
ALTER TABLE units 
ADD COLUMN IF NOT EXISTS maps_url TEXT DEFAULT NULL;

-- 2. Atualize a documentação da coluna
COMMENT ON COLUMN units.maps_url IS 'Link do Google Maps para a localização da unidade';

-- 3. Atualize as unidades com os endereços fornecidos

-- Unidade 21: Distrito Industrial - Rondonópolis
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Distrito+Industrial+Augusto+B.+Razia'
WHERE id = 21;

-- Unidade 22: Rondonópolis (Vila São Sebastião I)
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Avenida+Paulista,+639+-+Vila+São+Sebastião+I,+Rondonópolis+-+MT'
WHERE id = 22;

-- Unidade 23: Primavera do Leste (Castelândia)
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Rua+do+Comércio,+1497+-+Castelândia,+Primavera+do+Leste+-+MT'
WHERE id = 23;

-- Unidade 24: Querência (Setor E)
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Rua+Amazonas,+53+-+Setor+E,+Querência+-+MT'
WHERE id = 24;

-- Unidade 25: Barra do Garças (Jardim Nova Barra Norte)
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Av.+Sen.+Valdon+Varjão,+s/n+-+Jardim+Nova+Barra+Norte,+Barra+do+Garças+-+MT'
WHERE id = 25;

-- Unidade 26: Barra do Garças (Centro)
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Av.+Ministro+João+Alberto,+1150+-+Centro,+Barra+do+Garças+-+MT'
WHERE id = 26;

-- Unidade 27: Várzea Grande (Capão do Pequi)
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Rodovia+dos+Imigrantes,+km+516+-+Capão+do+Pequi,+Várzea+Grande+-+MT'
WHERE id = 27;

-- Unidade 28: Confresa (Vila Nova)
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Av.+Brasil,+3209+-+Vila+Nova,+Confresa+-+MT'
WHERE id = 28;

-- Unidade 29: Água Boa (Bairro Industrial 1)
UPDATE units
SET maps_url = 'https://www.google.com/maps/place/Av.+Industrial,+370+-+Bairro+Industrial+I,+Água+Boa+-+MT'
WHERE id = 29;

-- 4. Verifique se as atualizações foram bem-sucedidas
SELECT id, name, maps_url 
FROM units 
WHERE maps_url IS NOT NULL
ORDER BY id;
