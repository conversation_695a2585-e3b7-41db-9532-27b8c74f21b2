import { NextResponse } from "next/server"
import { createServerSupabaseClient } from "@/lib/supabase"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("categoria")
    const brand = searchParams.get("marca")
    const minPrice = searchParams.get("preco_min")
    const maxPrice = searchParams.get("preco_max")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "12")

    const supabase = createServerSupabaseClient()

    // Construir a query base
    let query = supabase
      .from("products")
      .select(
        `
        id, 
        name, 
        slug, 
        price, 
        sale_price, 
        category_id,
        brand,
        product_images!inner(url, is_main)
      `,
        { count: "exact" },
      )
      .eq("active", true)
      .eq("product_images.is_main", true)

    // Aplicar filtros
    if (category) {
      // Primeiro, buscar o ID da categoria pelo slug
      const { data: categoryData } = await supabase.from("categories").select("id").eq("slug", category).single()

      if (categoryData) {
        query = query.eq("category_id", categoryData.id)
      }
    }

    if (brand) {
      query = query.eq("brand", brand)
    }

    if (minPrice) {
      query = query.gte("price", Number.parseFloat(minPrice))
    }

    if (maxPrice) {
      query = query.lte("price", Number.parseFloat(maxPrice))
    }

    // Aplicar paginação
    const from = (page - 1) * limit
    const to = from + limit - 1

    // Executar a query
    const { data, error, count } = await query.range(from, to).order("name")

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Formatar os dados
    const products = data.map((product) => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      sale_price: product.sale_price,
      image_url:
        product.product_images[0]?.url ||
        "/placeholder.svg?height=300&width=300&text=" + encodeURIComponent(product.name),
      category_id: product.category_id,
      brand: product.brand,
    }))

    return NextResponse.json({
      products,
      pagination: {
        total: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit),
      },
    })
  } catch (error) {
    console.error("Erro ao buscar produtos:", error)
    return NextResponse.json({ error: "Erro ao buscar produtos" }, { status: 500 })
  }
}
