'use client'

import { Admin<PERSON>eader } from "@/components/admin-header"
import { AdminSidebar } from "@/components/admin-sidebar"
import { SettingsForm } from "@/components/admin/settings-form"

export default function Settings() {
  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col">
      <AdminHeader />

      <div className="flex flex-1">
        <AdminSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-foreground">Configurações</h1>
            <p className="text-muted-foreground">G<PERSON><PERSON><PERSON> as configurações do site</p>
          </div>

          <SettingsForm />
        </main>
      </div>
    </div>
  )
}
