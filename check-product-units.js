import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://lmlgxzfqnysvpsdewuag.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxtbGd4emZxbnlzdnBzZGV3dWFnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2MjUxNjMsImV4cCI6MjA2MzIwMTE2M30.nPRaojAAzcTxZwZntr_ieGeIwfxXOBu_HjeBoTVkZqw'

const supabase = createClient(supabaseUrl, supabaseKey)

async function checkProductUnits() {
  console.log('🔍 Verificando unidades para o produto ID 69...\n')
  
  try {
    // Verificar se o produto existe
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('id, name, sku')
      .eq('id', 69)
      .single()

    if (productError) {
      console.error('❌ Erro ao buscar produto:', productError.message)
      return
    }

    if (!product) {
      console.log('⚠️ Produto ID 69 não encontrado')
      return
    }

    console.log('📦 Produto encontrado:')
    console.log(`   ID: ${product.id}`)
    console.log(`   Nome: ${product.name}`)
    console.log(`   SKU: ${product.sku}\n`)

    // Verificar unidades associadas
    const { data: productUnits, error: unitsError } = await supabase
      .from('product_units')
      .select(`
        unit_id,
        units(id, name, city, state, active)
      `)
      .eq('product_id', 69)

    if (unitsError) {
      console.error('❌ Erro ao buscar unidades do produto:', unitsError.message)
      return
    }

    console.log('🏢 Unidades associadas ao produto:')
    if (!productUnits || productUnits.length === 0) {
      console.log('   ⚠️ NENHUMA unidade associada a este produto!')
      console.log('   📝 Isso explica por que o modal mostra "Nenhuma unidade disponível"')
    } else {
      productUnits.forEach(pu => {
        const unit = pu.units
        console.log(`   📍 ${unit.name} (ID: ${unit.id}) - ${unit.city}/${unit.state} - Ativo: ${unit.active}`)
      })
    }

    // Verificar todas as unidades ativas disponíveis
    console.log('\n🏢 Todas as unidades ativas no sistema:')
    const { data: allUnits, error: allUnitsError } = await supabase
      .from('units')
      .select('id, name, city, state')
      .eq('active', true)
      .order('name')

    if (allUnitsError) {
      console.error('❌ Erro ao buscar todas as unidades:', allUnitsError.message)
    } else if (allUnits && allUnits.length > 0) {
      allUnits.forEach(unit => {
        console.log(`   📍 ${unit.name} (ID: ${unit.id}) - ${unit.city}/${unit.state}`)
      })
      
      console.log('\n💡 SOLUÇÃO:')
      console.log('Para que este produto apareça no modal, você precisa associá-lo a uma ou mais unidades.')
      console.log('Você pode fazer isso no painel admin, editando o produto e selecionando as unidades.')
    } else {
      console.log('   ⚠️ Nenhuma unidade ativa encontrada no sistema')
    }

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

checkProductUnits()
